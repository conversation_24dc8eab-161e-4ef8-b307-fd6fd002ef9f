#!/usr/bin/env python3
"""
测试配置和数据加载
"""

import json
import os

def test_best_personas():
    """测试best_persona.json文件"""
    try:
        with open('best_persona.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("✓ best_persona.json 加载成功")
        print(f"包含 {len(data)} 个学科:")
        
        for subject, personas in data.items():
            print(f"  {subject}: {list(personas.keys())}")
            for persona, info in personas.items():
                print(f"    - {persona}: {info['accuracy']:.4f}")
        
        return True
    except Exception as e:
        print(f"✗ best_persona.json 加载失败: {e}")
        return False

def test_data_directories():
    """测试数据目录"""
    target_subjects = ["abstract_algebra", "college_mathmetics", "formal_logic", "human_sexuality", "philosophy"]
    
    print("\n检查数据目录:")
    found_subjects = []
    
    for subject in target_subjects:
        if os.path.isdir(subject):
            jsonl_files = [f for f in os.listdir(subject) if f.endswith('.jsonl')]
            if jsonl_files:
                print(f"✓ {subject}: 找到 {len(jsonl_files)} 个.jsonl文件")
                found_subjects.append(subject)
            else:
                print(f"✗ {subject}: 目录存在但没有.jsonl文件")
        else:
            print(f"✗ {subject}: 目录不存在")
    
    return found_subjects

def test_persona_prompts():
    """测试persona提示词"""
    from gen_mmlu import create_persona_prompts, load_best_personas
    
    print("\n检查persona提示词:")
    
    try:
        best_personas = load_best_personas()
        persona_prompts = create_persona_prompts()
        
        # 收集所有使用的persona
        used_personas = set()
        for subject_personas in best_personas.values():
            used_personas.update(subject_personas.keys())
        
        missing_prompts = []
        for persona in used_personas:
            if persona in persona_prompts:
                print(f"✓ {persona}: 提示词已定义")
            else:
                print(f"✗ {persona}: 缺少提示词定义")
                missing_prompts.append(persona)
        
        return len(missing_prompts) == 0
        
    except Exception as e:
        print(f"✗ 测试persona提示词时出错: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("MMLU配置测试")
    print("=" * 60)
    
    # 测试配置文件
    config_ok = test_best_personas()
    
    # 测试数据目录
    found_subjects = test_data_directories()
    
    # 测试persona提示词
    prompts_ok = test_persona_prompts()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if config_ok:
        print("✓ 配置文件正常")
    else:
        print("✗ 配置文件有问题")
    
    if found_subjects:
        print(f"✓ 找到 {len(found_subjects)} 个可用学科: {found_subjects}")
    else:
        print("✗ 没有找到可用的学科数据")
    
    if prompts_ok:
        print("✓ Persona提示词完整")
    else:
        print("✗ Persona提示词不完整")
    
    if config_ok and found_subjects and prompts_ok:
        print("\n🎉 所有测试通过，可以运行 gen_mmlu.py")
    else:
        print("\n⚠️  存在问题，请检查后再运行")
