# MMLU多Agent测试配置系统

## 概述

这个系统允许您为每个MMLU学科分类配置不同的persona组合和题目数量，实现个性化的测试设置。

## 文件说明

- `gen_mmlu.py` - 主程序，执行MMLU测试
- `subject_configs.json` - 配置文件，包含所有persona和学科设置
- `config_manager.py` - 配置管理工具，用于查看和修改配置
- `README_配置说明.md` - 本说明文件

## 配置文件结构

### Persona定义
系统提供6种不同的persona：

0. **Analytical Thinker** - 分析思考者：系统性地分解问题，逐步推理
1. **Creative Problem Solver** - 创意问题解决者：跳出框架思考，寻找创新方法
2. **Detail-Oriented Validator** - 细节导向验证者：专注准确性和精确性
3. **Expert Scholar** - 专家学者：具有深厚学术知识和研究经验
4. **Practical Strategist** - 实用策略家：关注实际应用和结果
5. **Critical Evaluator** - 批判评估者：质疑假设，仔细检查证据

### 学科配置
每个学科可以配置：
- `personas`: 使用的persona索引列表（如[0,1,3]表示使用persona 0、1、3）
- `max_questions`: 最多处理的题目数量
- `description`: 学科描述

## 使用方法

### 1. 运行主程序
```bash
python gen_mmlu.py
```

程序会：
1. 加载配置文件
2. 显示配置摘要
3. 按学科处理题目
4. 为每个学科生成单独的结果文件

### 2. 使用配置管理工具
```bash
python config_manager.py
```

功能包括：
- 查看所有可用persona
- 查看所有学科配置
- 修改特定学科配置
- 批量设置最大题目数
- 保存配置

### 3. 手动编辑配置文件
直接编辑 `subject_configs.json` 文件来修改配置。

## 配置示例

### 数学类学科配置
```json
"college_mathematics": {
  "personas": [0, 2, 3],  // 分析思考者 + 细节验证者 + 专家学者
  "max_questions": 45,
  "description": "大学数学"
}
```

### 人文类学科配置
```json
"philosophy": {
  "personas": [1, 3, 5],  // 创意解决者 + 专家学者 + 批判评估者
  "max_questions": 35,
  "description": "哲学"
}
```

### 理工类学科配置
```json
"electrical_engineering": {
  "personas": [0, 1, 4],  // 分析思考者 + 创意解决者 + 实用策略家
  "max_questions": 40,
  "description": "电气工程"
}
```

## 输出文件命名

结果文件按以下格式命名：
```
mmlu_{学科名}_{persona组合}_{agent数量}agents_{题目数量}q_{轮数}r.json
```

例如：
```
mmlu_college_mathematics_AnalyticalThinkerDetail-OrientedValidatorExpertScholar_3agents_45q_1r.json
```

## 自定义配置建议

### 按学科特点选择persona：

**数学/逻辑类**：
- Analytical Thinker (0) - 系统性分析
- Detail-Oriented Validator (2) - 验证计算
- Expert Scholar (3) - 学术知识

**创意/人文类**：
- Creative Problem Solver (1) - 创新思维
- Expert Scholar (3) - 学术背景
- Critical Evaluator (5) - 批判思考

**应用/工程类**：
- Analytical Thinker (0) - 分析能力
- Practical Strategist (4) - 实用性
- Creative Problem Solver (1) - 创新方案

**医学/生物类**：
- Expert Scholar (3) - 专业知识
- Detail-Oriented Validator (2) - 精确性
- Analytical Thinker (0) - 系统分析

### 题目数量建议：
- 快速测试：10-20题
- 标准测试：30-50题
- 完整测试：所有题目（设置较大数值）

## 注意事项

1. **API调用限制**：注意OpenAI API的调用频率限制
2. **配置验证**：确保persona索引在有效范围内（0-5）
3. **备份配置**：修改前建议备份配置文件
4. **结果文件**：每次运行会覆盖同名结果文件

## 故障排除

### 配置文件错误
如果配置文件损坏或缺失，程序会自动使用默认配置。

### Persona索引错误
如果配置中的persona索引超出范围，程序会跳过无效索引。

### 题目数量设置
如果设置的题目数量超过实际可用题目，程序会处理所有可用题目。
