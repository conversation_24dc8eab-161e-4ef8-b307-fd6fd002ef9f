# MMLU多Agent测试系统使用说明

## 功能概述

这个系统会测试5个MMLU学科分类，每个学科使用 `best_persona.json` 中配置的最佳persona组合，每完成5个问题就自动保存进度，防止意外中断导致重复测试。

## 目标学科

- `abstract_algebra` - 抽象代数
- `college_mathmetics` - 大学数学  
- `formal_logic` - 形式逻辑
- `human_sexuality` - 人类性学
- `philosophy` - 哲学

## 文件说明

### 核心文件
- `gen_mmlu.py` - 主程序
- `best_persona.json` - 最佳persona配置（按准确率排序）
- `test_config.py` - 配置测试脚本

### 数据目录
每个学科需要有对应的目录，包含 `.jsonl` 格式的题目文件：
```
abstract_algebra/
├── test.jsonl
└── ...

college_mathmetics/
├── test.jsonl
└── ...

formal_logic/
├── test.jsonl
└── ...

human_sexuality/
├── test.jsonl
└── ...

philosophy/
├── test.jsonl
└── ...
```

## 使用步骤

### 1. 测试配置
运行测试脚本检查配置是否正确：
```bash
python test_config.py
```

### 2. 运行主程序
```bash
python gen_mmlu.py
```

## 程序特性

### 自动进度保存
- 每完成5个问题自动保存进度文件
- 进度文件格式：`mmlu_{学科名}_progress_{问题数}.json`
- 程序重启时会自动从断点继续

### 断点续传
- 如果程序意外中断，重新运行会自动检测进度文件
- 从上次中断的地方继续，不会重复处理已完成的问题

### 结果文件
- 最终结果文件：`mmlu_{学科名}_final_{问题数}questions.json`
- 包含每个问题的完整对话上下文和正确答案
- 程序完成后会自动清理进度文件

## 输出文件结构

```json
{
  "问题文本": {
    "agent_contexts": [
      [
        {"role": "system", "content": "persona提示词"},
        {"role": "user", "content": "问题"},
        {"role": "assistant", "content": "回答"}
      ],
      // ... 其他agent的对话
    ],
    "correct_answer": "A",
    "question_index": 0,
    "personas_used": ["logician", "Psychologist", "Autistic Savant"]
  }
}
```

## Persona配置

每个学科使用的persona按准确率从高到低排序：

### abstract_algebra
1. logician (0.6724)
2. Psychologist (0.6552)
3. Autistic Savant (0.6552)

### college_mathmetics  
1. Statistician (0.8017)
2. Systematic Thinker (0.7500)
3. Genius (0.7500)

### formal_logic
1. Autistic Savant (0.8276)
2. Computer Scientist (0.7862)
3. Physicist (0.7517)

### human_sexuality
1. Atheist (0.8851)
2. Physicist (0.7770)
3. Logician (0.6554)

### philosophy
1. Religious (0.8200)
2. Architect (0.8029)
3. Statistician (0.7800)

## 注意事项

### API配置
- 确保OpenAI API密钥有效
- 注意API调用频率限制
- 程序会在出错时自动重试

### 数据准备
- 确保所有目标学科的数据目录存在
- 每个目录至少包含一个 `.jsonl` 文件
- 文件格式需要符合MMLU标准

### 存储空间
- 每个学科的结果文件可能较大
- 确保有足够的磁盘空间
- 进度文件会在完成后自动清理

## 故障排除

### 配置文件错误
```bash
python test_config.py
```
运行测试脚本检查配置问题

### API调用失败
- 检查网络连接
- 验证API密钥
- 查看API服务状态

### 数据文件问题
- 确认目录结构正确
- 检查 `.jsonl` 文件格式
- 验证文件编码为UTF-8

### 进度恢复问题
- 手动删除损坏的进度文件
- 重新运行程序从头开始

## 预期运行时间

根据每个学科的问题数量和API响应时间，预计：
- 每个问题需要3-5秒（3个agent）
- 每个学科可能需要几分钟到几小时
- 总体完成时间取决于数据量和网络状况
