import json
import time
from openai import OpenAI
import os


openai_api = 'sk-HUF91JMbN3vueqscHdKk6qzQZukcveNaAXl4X6sZsPZBCayB'

def load_best_personas():
    """从best_persona.json加载最佳persona配置"""
    try:
        with open('best_persona.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: best_persona.json not found!")
        return {}

def create_persona_prompts():
    """创建persona提示词字典"""
    return {
        "logician": "You are a logician who approaches problems with formal reasoning and logical analysis. You identify premises, apply logical rules, and draw valid conclusions. You are precise in your reasoning and avoid logical fallacies.",
        "Logician": "You are a logician who approaches problems with formal reasoning and logical analysis. You identify premises, apply logical rules, and draw valid conclusions. You are precise in your reasoning and avoid logical fallacies.",
        "Psychologist": "You are a psychologist who understands human behavior, cognition, and mental processes. You analyze problems through the lens of psychological principles and human nature.",
        "Autistic Savant": "You are an autistic savant with exceptional analytical abilities and attention to detail. You process information systematically and notice patterns others might miss.",
        "Statistician": "You are a statistician who approaches problems through data analysis, probability, and statistical reasoning. You quantify uncertainty and make evidence-based conclusions.",
        "Systematic Thinker": "You are a systematic thinker who breaks down complex problems into manageable components. You follow structured approaches and methodical analysis.",
        "Genius": "You are a genius with exceptional intellectual capabilities. You can quickly grasp complex concepts and find innovative solutions to challenging problems.",
        "Physicist": "You are a physicist who applies scientific principles and mathematical reasoning to understand and solve problems. You think in terms of fundamental laws and relationships.",
        "Computer Scientist": "You are a computer scientist who approaches problems algorithmically. You think in terms of computational processes, logic, and systematic problem-solving.",
        "Atheist": "You are an atheist who approaches problems with rational, evidence-based thinking. You rely on scientific reasoning and logical analysis without religious considerations.",
        "Architect": "You are an architect who thinks structurally and systematically. You consider how different components fit together and design comprehensive solutions.",
        "Religious": "You are a religious person who incorporates moral and ethical considerations into your reasoning. You consider broader implications and values in your analysis."
    }

# 目标学科列表
TARGET_SUBJECTS = ["abstract_algebra", "college_mathmetics", "formal_logic", "human_sexuality", "philosophy"]

def construct_message_with_previous_answers(previous_responses, question):
    """构建包含之前agent回答的消息"""
    if len(previous_responses) == 0:
        # 第一个agent，没有参考答案
        return {"role": "user", "content": question}

    prefix_string = "Here are the solutions from previous agents:\n"

    for i, response in enumerate(previous_responses):
        prefix_string += f"\nAgent {i+1} solution:\n```{response}```\n"

    prefix_string += f"\nNow, considering the above solutions as reference, please provide your own answer to the question: {question}\n"
    prefix_string += "Put your final answer in the form (X) at the end of your response."

    return {"role": "user", "content": prefix_string}


def generate_answer(answer_context):
    """生成AI回答"""
    try:
        client = OpenAI(api_key=openai_api, base_url="https://api2.aigcbest.top/v1")
        response = client.chat.completions.create(
            model='gpt-4o',
            messages=answer_context,  # 修复：应该是messages而不是message
            stream=False,
            temperature=0.0  # 修复：应该是temperature而不是tempreture
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"API调用出错: {e}, 5秒后重试...")
        time.sleep(5)
        return generate_answer(answer_context)

def save_progress(subject_name, response_dict, question_count):
    """保存进度到文件"""
    filename = f"mmlu_{subject_name}_progress_{question_count}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(response_dict, f, ensure_ascii=False, indent=2)
    print(f"  进度已保存到 {filename}")

def load_progress(subject_name):
    """加载已有的进度文件"""
    progress_files = [f for f in os.listdir('.') if f.startswith(f"mmlu_{subject_name}_progress_")]
    if not progress_files:
        return {}, 0

    # 找到最新的进度文件
    latest_file = max(progress_files, key=lambda x: int(x.split('_')[-1].split('.')[0]))
    latest_count = int(latest_file.split('_')[-1].split('.')[0])

    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            response_dict = json.load(f)
        print(f"  从 {latest_file} 加载进度，已完成 {latest_count} 个问题")
        return response_dict, latest_count
    except:
        return {}, 0


def load_jsonl_data_by_subject(data_dir):
    """按学科分别加载所有jsonl格式的MMLU数据"""
    subjects_data = {}
    for subject_dir in os.listdir(data_dir):
        subject_path = os.path.join(data_dir, subject_dir)
        if os.path.isdir(subject_path):
            jsonl_files = [f for f in os.listdir(subject_path) if f.endswith('.jsonl')]
            for jsonl_file in jsonl_files:
                file_path = os.path.join(subject_path, jsonl_file)
                subject_name = subject_dir
                if subject_name not in subjects_data:
                    subjects_data[subject_name] = []

                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        subjects_data[subject_name].append(data)
    return subjects_data

def parse_question_answer(data_item):
    """从jsonl数据项中解析问题和答案"""
    question = data_item["question"]
    choices = data_item["choices"]

    # 格式化问题
    formatted_question = "Can you answer the following question as accurately as possible? {}: A) {}, B) {}, C) {}, D) {} Explain your answer, putting the answer in the form (X) at the end of your response.".format(
        question, choices[0], choices[1], choices[2], choices[3]
    )

    # 答案索引转换为字母
    answer_idx = data_item["answer"]
    answer_letter = chr(ord('A') + answer_idx)

    return formatted_question, answer_letter

def create_agent_context(persona, question):
    """为特定persona创建初始对话上下文"""
    return [
        {"role": "system", "content": persona["prompt"]},
        {"role": "user", "content": question}
    ]

if __name__ == "__main__":
    # 加载最佳persona配置和提示词
    best_personas = load_best_personas()
    persona_prompts = create_persona_prompts()

    if not best_personas:
        print("无法加载best_persona.json，程序退出")
        exit(1)

    # 加载MMLU数据（按学科分类）
    data_dir = "."
    subjects_data = load_jsonl_data_by_subject(data_dir)

    print(f"加载了 {len(subjects_data)} 个学科的数据")

    # 只处理目标学科
    target_subjects_data = {subject: subjects_data[subject] for subject in TARGET_SUBJECTS if subject in subjects_data}

    if not target_subjects_data:
        print("未找到目标学科数据，请检查数据目录")
        exit(1)

    print(f"目标学科数据:")
    for subject, data in target_subjects_data.items():
        print(f"  {subject}: {len(data)} 个问题")

    # 为每个目标学科分别处理
    for subject_name, subject_data in target_subjects_data.items():
        if subject_name not in best_personas:
            print(f"警告: {subject_name} 在best_persona.json中未找到配置，跳过")
            continue

        print(f"\n{'='*80}")
        print(f"开始处理学科: {subject_name.upper()}")
        print(f"总问题数: {len(subject_data)}")

        # 获取该学科的persona配置
        subject_personas = best_personas[subject_name]
        persona_names = list(subject_personas.keys())
        agents = len(persona_names)

        print(f"使用的Persona: {persona_names}")
        print(f"Agent数量: {agents}")
        print(f"{'='*80}")

        # 加载已有进度
        response_dict, start_question = load_progress(subject_name)

        # 从断点继续处理问题
        for i in range(start_question, len(subject_data)):
            data_item = subject_data[i]
            question, answer = parse_question_answer(data_item)

            print(f"\n处理问题 {i+1}/{len(subject_data)}...")
            print(f"问题: {data_item['question'][:100]}...")

            # 存储每个agent的完整对话上下文
            agent_contexts = []
            # 存储每个agent的回答内容（用于后续agent参考）
            previous_responses = []

            # 每个agent依次回答
            for j, persona_name in enumerate(persona_names):
                print(f"  Agent {j+1} ({persona_name}) 正在思考...")

                # 创建当前agent的上下文
                agent_context = [{"role": "system", "content": persona_prompts[persona_name]}]

                # 构建包含之前agent回答的消息
                message = construct_message_with_previous_answers(previous_responses, question)
                agent_context.append(message)

                # 获取当前agent的回答
                response_content = generate_answer(agent_context)
                assistant_message = {"role": "assistant", "content": response_content}
                agent_context.append(assistant_message)

                # 保存完整的对话上下文
                agent_contexts.append(agent_context)

                # 保存回答内容供后续agent参考
                previous_responses.append(response_content)

                print(f"  Agent {j+1} 回答: {response_content[:80]}...")

            # 保存问题和回答
            response_dict[question] = {
                "agent_contexts": agent_contexts,
                "correct_answer": answer,
                "question_index": i,
                "personas_used": persona_names
            }

            print(f"  问题 {i+1} 完成. 正确答案: {answer}")

            # 每5个问题保存一次进度
            if (i + 1) % 5 == 0:
                save_progress(subject_name, response_dict, i + 1)

        # 保存最终结果
        final_filename = f"mmlu_{subject_name}_final_{len(response_dict)}questions.json"
        with open(final_filename, 'w', encoding='utf-8') as f:
            json.dump(response_dict, f, ensure_ascii=False, indent=2)

        print(f"\n学科 {subject_name} 处理完成!")
        print(f"最终结果保存到 {final_filename}")
        print(f"总共处理了 {len(response_dict)} 个问题")

        # 清理进度文件
        progress_files = [f for f in os.listdir('.') if f.startswith(f"mmlu_{subject_name}_progress_")]
        for pf in progress_files:
            try:
                os.remove(pf)
                print(f"已清理进度文件: {pf}")
            except:
                pass

    print(f"\n{'='*80}")
    print("所有目标学科处理完成!")
    print(f"{'='*80}")
