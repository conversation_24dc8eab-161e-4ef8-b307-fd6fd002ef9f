import json
import time
import openai
import os

# 定义三个固定的persona
PERSONAS = [
    {
        "name": "Analytical Thinker",
        "prompt": "You are an analytical thinker who approaches problems systematically. You break down complex questions into smaller parts, analyze each component carefully, and use logical reasoning to arrive at conclusions. Always explain your step-by-step thinking process."
    },
    {
        "name": "Creative Problem Solver",
        "prompt": "You are a creative problem solver who thinks outside the box. You look for patterns, make connections between seemingly unrelated concepts, and often find innovative approaches to problems. You consider multiple perspectives before reaching a conclusion."
    },
    {
        "name": "Detail-Oriented Validator",
        "prompt": "You are a detail-oriented validator who focuses on accuracy and precision. You carefully check facts, verify calculations, and look for potential errors or oversights. You are methodical and thorough in your analysis."
    }
]

def construct_message_with_previous_answers(previous_responses, question):
    """构建包含之前agent回答的消息"""
    if len(previous_responses) == 0:
        # 第一个agent，没有参考答案
        return {"role": "user", "content": question}

    prefix_string = "Here are the solutions from previous agents:\n"

    for i, response in enumerate(previous_responses):
        prefix_string += f"\nAgent {i+1} solution:\n```{response}```\n"

    prefix_string += f"\nNow, considering the above solutions as reference, please provide your own answer to the question: {question}\n"
    prefix_string += "Put your final answer in the form (X) at the end of your response."

    return {"role": "user", "content": prefix_string}


def construct_assistant_message(completion):
    content = completion["choices"][0]["message"]["content"]
    return {"role": "assistant", "content": content}


def generate_answer(answer_context):
    try:
        completion = openai.ChatCompletion.create(
                  model="gpt-3.5-turbo-0301",
                  messages=answer_context,
                  n=1)
    except:
        print("retrying due to an error......")
        time.sleep(20)
        return generate_answer(answer_context)

    return completion


def load_jsonl_data_by_subject(data_dir):
    """按学科分别加载所有jsonl格式的MMLU数据"""
    subjects_data = {}
    for subject_dir in os.listdir(data_dir):
        subject_path = os.path.join(data_dir, subject_dir)
        if os.path.isdir(subject_path):
            jsonl_files = [f for f in os.listdir(subject_path) if f.endswith('.jsonl')]
            for jsonl_file in jsonl_files:
                file_path = os.path.join(subject_path, jsonl_file)
                subject_name = subject_dir
                if subject_name not in subjects_data:
                    subjects_data[subject_name] = []

                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        subjects_data[subject_name].append(data)
    return subjects_data

def parse_question_answer(data_item):
    """从jsonl数据项中解析问题和答案"""
    question = data_item["question"]
    choices = data_item["choices"]

    # 格式化问题
    formatted_question = "Can you answer the following question as accurately as possible? {}: A) {}, B) {}, C) {}, D) {} Explain your answer, putting the answer in the form (X) at the end of your response.".format(
        question, choices[0], choices[1], choices[2], choices[3]
    )

    # 答案索引转换为字母
    answer_idx = data_item["answer"]
    answer_letter = chr(ord('A') + answer_idx)

    return formatted_question, answer_letter

def create_agent_context(persona, question):
    """为特定persona创建初始对话上下文"""
    return [
        {"role": "system", "content": persona["prompt"]},
        {"role": "user", "content": question}
    ]

if __name__ == "__main__":
    # 固定参数
    agents = 3
    rounds = 1  # 只辩论一轮

    # 加载MMLU数据（按学科分类）
    data_dir = "."  # 当前目录下的各个学科文件夹
    subjects_data = load_jsonl_data_by_subject(data_dir)

    print(f"Loaded {len(subjects_data)} subjects:")
    for subject, data in subjects_data.items():
        print(f"  {subject}: {len(data)} questions")

    # 为每个学科分别处理所有题目
    for subject_name, subject_data in subjects_data.items():
        print(f"\n{'='*60}")
        print(f"Processing subject: {subject_name.upper()}")
        print(f"Total questions: {len(subject_data)}")
        print(f"{'='*60}")

        response_dict = {}

        # 处理该学科的所有问题
        for i, data_item in enumerate(subject_data):
            question, answer = parse_question_answer(data_item)

            print(f"\nProcessing question {i+1}/{len(subject_data)}...")
            print(f"Question: {data_item['question'][:100]}...")

            # 存储每个agent的完整对话上下文
            agent_contexts = []
            # 存储每个agent的回答内容（用于后续agent参考）
            previous_responses = []

            # 一轮辩论：每个agent依次回答，后面的agent可以看到前面agent的回答
            for j in range(agents):
                print(f"  Agent {j+1} ({PERSONAS[j]['name']}) is thinking...")

                # 创建当前agent的上下文
                agent_context = [{"role": "system", "content": PERSONAS[j]["prompt"]}]

                # 构建包含之前agent回答的消息
                message = construct_message_with_previous_answers(previous_responses, question)
                agent_context.append(message)

                # 获取当前agent的回答
                completion = generate_answer(agent_context)
                assistant_message = construct_assistant_message(completion)
                agent_context.append(assistant_message)

                # 保存完整的对话上下文
                agent_contexts.append(agent_context)

                # 保存回答内容供后续agent参考
                previous_responses.append(assistant_message['content'])

                print(f"  Agent {j+1} response: {assistant_message['content'][:80]}...")

            response_dict[question] = (agent_contexts, answer)
            print(f"  Question {i+1} completed. Correct answer: {answer}")

        # 为每个学科保存单独的结果文件
        output_filename = f"mmlu_personas_{subject_name}_{agents}_{rounds}.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(response_dict, f, ensure_ascii=False, indent=2)

        print(f"\nSubject {subject_name} completed!")
        print(f"Results saved to {output_filename}")
        print(f"Processed {len(response_dict)} questions")

    print(f"\n{'='*60}")
    print("ALL SUBJECTS COMPLETED!")
    print(f"{'='*60}")
